# AgentsUI MVP 需求文档

## 1. MVP 概述

### 1.1 MVP 目标

基于现有项目需求，开发一个最小可行产品（MVP），快速验证核心功能和用户体验。MVP 专注于最核心的 Agent 管理和对话功能，为后续迭代奠定基础。

### 1.2 MVP 范围

**包含功能**：
- 基础 Agent 管理（发现、启动、停止）
- 简单的用户对话界面
- 基本的 WebSocket 实时通信
- 核心的身份认证

**暂不包含**：
- 复杂的知识库管理
- 高级的权限控制
- 详细的监控和日志
- 移动端适配

## 2. MVP 功能需求

### 2.1 管理端（Admin）

#### 2.1.1 基础布局
```
┌─────────────────────────────────────────────────────────┐
│ Header: Logo + 导航 + 用户信息                            │
├─────────────┬───────────────────────────────────────────┤
│ 侧边导航     │ 主内容区域                                  │
│ - Agents   │                                           │
│ - 设置      │                                           │
└─────────────┴───────────────────────────────────────────┘
```

#### 2.1.2 Agent 管理功能

**Agent 列表页面**：
- 显示 Agents/ 目录下所有 Agent
- 显示 Agent 基本信息：名称、状态、描述
- 支持 Agent 状态切换：启动/停止
- 简单的搜索和筛选功能

**Agent 状态管理**：
- 未加载（灰色）
- 运行中（绿色）
- 已停止（红色）
- 错误状态（橙色）

**核心操作**：
- 启动 Agent：调用 Agent 的启动接口
- 停止 Agent：停止 Agent 服务
- 查看日志：显示 Agent 运行日志
- 重启 Agent：重新启动 Agent 服务

#### 2.1.3 基础设置

**系统设置**：
- 用户管理（添加、删除用户）
- 基本系统配置
- Agent 全局配置

### 2.2 用户端（User）

#### 2.2.1 基础布局
```
┌─────────────────────────────────────────────────────────┐
│ Header: Logo + 当前 Agent + 用户信息                      │
├─────────┬─────────┬─────────────────────────────────────┤
│ Agent   │ 会话    │ 对话区域                              │
│ 列表    │ 历史    │                                     │
│         │         │                                     │
│         │         │                                     │
│         │         ├─────────────────────────────────────┤
│         │         │ 输入区域                              │
└─────────┴─────────┴─────────────────────────────────────┘
```

#### 2.2.2 Agent 选择

**Agent 列表**：
- 显示所有可用的 Agent
- 显示 Agent 状态和基本信息
- 支持 Agent 切换

#### 2.2.3 对话功能

**基础对话**：
- 文本消息发送和接收
- 实时 WebSocket 通信
- 基础的 Markdown 渲染
- 消息时间戳显示

**会话管理**：
- 创建新会话
- 会话历史列表
- 会话切换
- 会话删除

**消息类型**：
- 用户消息（右对齐，蓝色背景）
- Agent 消息（左对齐，灰色背景）
- 系统消息（居中，浅色背景）

## 3. MVP 技术架构

### 3.1 技术栈选择

**前端**：
- React 18 + TypeScript
- Ant Design 5.x（快速 UI 开发）
- Socket.io-client（WebSocket 通信）
- Axios（HTTP 请求）

**后端**：
- FastAPI（主服务）
- SQLite（简化数据库部署）
- Redis（会话存储，可选）
- WebSocket（实时通信）

**部署**：
- Docker + Docker Compose
- Nginx（反向代理）

### 3.2 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   前端 UI    │    │  API 网关   │    │   Agent     │
│   (React)   │◄──►│  (FastAPI)  │◄──►│  Services   │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                   ┌─────────────┐
                   │  数据库      │
                   │ (SQLite)    │
                   └─────────────┘
```

### 3.3 数据模型（简化版）

#### 3.3.1 用户表
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.3.2 Agent 表
```sql
CREATE TABLE agents (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    path VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'stopped',
    port INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.3.3 会话表
```sql
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    agent_id INTEGER REFERENCES agents(id),
    title VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.3.4 消息表
```sql
CREATE TABLE messages (
    id INTEGER PRIMARY KEY,
    session_id INTEGER REFERENCES sessions(id),
    sender_type VARCHAR(10) NOT NULL, -- 'user' or 'agent'
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 4. MVP API 设计

### 4.1 认证接口

```
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/me
```

### 4.2 Agent 管理接口

```
GET    /api/agents              # 获取 Agent 列表
POST   /api/agents/{id}/start   # 启动 Agent
POST   /api/agents/{id}/stop    # 停止 Agent
GET    /api/agents/{id}/status  # 获取 Agent 状态
GET    /api/agents/{id}/logs    # 获取 Agent 日志
```

### 4.3 会话管理接口

```
GET    /api/sessions            # 获取用户会话列表
POST   /api/sessions            # 创建新会话
GET    /api/sessions/{id}       # 获取会话详情
DELETE /api/sessions/{id}       # 删除会话
GET    /api/sessions/{id}/messages # 获取会话消息
```

### 4.4 WebSocket 接口

```
WS /ws/{session_id}             # 会话 WebSocket 连接
```

**消息格式**：
```json
{
  "type": "user_message|agent_response|system_message",
  "content": "消息内容",
  "timestamp": "2024-01-01T00:00:00Z",
  "metadata": {}
}
```

## 5. MVP 开发计划

### 5.1 开发阶段

**第一阶段（1-2周）**：基础框架搭建
- 项目初始化和环境配置
- 基础前端框架搭建
- 后端 API 框架搭建
- 数据库设计和初始化

**第二阶段（2-3周）**：核心功能开发
- Agent 发现和管理功能
- 基础认证系统
- 用户界面开发
- WebSocket 通信实现

**第三阶段（1-2周）**：对话功能实现
- 对话界面开发
- 会话管理功能
- Agent 集成和测试
- 基础错误处理

**第四阶段（1周）**：测试和优化
- 功能测试和 Bug 修复
- 性能优化
- 部署配置
- 文档编写

### 5.2 里程碑

- **里程碑 1**：完成基础框架和 Agent 发现
- **里程碑 2**：完成 Agent 启停管理
- **里程碑 3**：完成基础对话功能
- **里程碑 4**：完成 MVP 部署和测试

## 6. MVP 验收标准

### 6.1 功能验收

**管理端**：
- [ ] 能够发现 Agents/ 目录下的所有 Agent
- [ ] 能够启动和停止 Agent 服务
- [ ] 能够查看 Agent 运行状态
- [ ] 能够进行基础的用户管理

**用户端**：
- [ ] 能够选择可用的 Agent
- [ ] 能够创建和管理对话会话
- [ ] 能够发送消息并接收 Agent 响应
- [ ] 能够查看历史对话记录

### 6.2 性能验收

- [ ] 页面加载时间 < 3秒
- [ ] API 响应时间 < 1秒
- [ ] 支持 10+ 并发用户
- [ ] WebSocket 连接稳定

### 6.3 兼容性验收

- [ ] Chrome 90+ 正常运行
- [ ] Firefox 88+ 正常运行
- [ ] 基础的响应式设计

## 7. MVP 后续规划

### 7.1 下一版本功能

**v1.1 计划**：
- 知识库基础管理
- 文档上传和解析
- 改进的用户界面
- 更好的错误处理

**v1.2 计划**：
- 向量化搜索功能
- 高级权限管理
- 系统监控和日志
- 移动端适配

### 7.2 技术债务

**需要在后续版本中解决**：
- 数据库迁移到 PostgreSQL
- 完善的错误处理和日志
- 单元测试和集成测试
- 安全性加强
- 性能优化

## 8. 风险评估

### 8.1 技术风险

**高风险**：
- Agent 服务的稳定性和兼容性
- WebSocket 连接的可靠性

**中风险**：
- 前后端集成复杂度
- 数据库性能瓶颈

**低风险**：
- UI 组件开发
- 基础 API 开发

### 8.2 缓解措施

- 充分的 Agent 集成测试
- WebSocket 重连机制
- 分阶段开发和测试
- 及时的技术评审

## 9. 总结

本 MVP 需求文档定义了 AgentsUI 项目的最小可行产品范围，专注于核心的 Agent 管理和对话功能。通过分阶段开发，可以快速验证产品概念，为后续功能迭代提供坚实基础。

MVP 的成功将为项目后续发展提供重要的用户反馈和技术验证，确保产品方向的正确性和技术架构的可行性。
